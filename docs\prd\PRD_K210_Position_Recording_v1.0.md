# K210矩形追踪系统位置记录与复位功能 PRD

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-08 |
| 负责人 | Emma (产品经理) |
| 项目名称 | K210位置记录与复位功能 |
| 文档类型 | 产品需求文档 (PRD) |

## 2. 背景与问题陈述

### 2.1 项目背景
当前K210矩形追踪系统已实现基础的矩形检测和舵机追踪功能，但缺少位置记录和复位能力。用户需要在第一次检测到矩形框时锁定并记录位置，并能在任何时候让舵机回复到记录的位置。

### 2.2 核心问题
- 无法记录和保存矩形框的锁定位置
- 舵机断电后无法恢复到之前的记录位置
- 缺少位置数据的持久化存储机制
- 没有用户交互接口进行手动控制

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **位置记录能力**：实现首次检测矩形框时的位置锁定和记录
2. **数据持久化**：确保位置数据在断电后能够可靠恢复
3. **复位功能**：提供可靠的舵机复位到记录位置的能力
4. **用户交互**：提供简单易用的控制接口

### 3.2 关键结果 (Key Results)
- 首次检测成功率 ≥ 95%
- 位置记录精度误差 ≤ ±5个舵机单位
- 断电恢复成功率 = 100%
- 复位操作成功率 ≥ 99%
- 系统响应时间 ≤ 100ms

### 3.3 反向指标 (Counter Metrics)
- 原有追踪性能不能下降超过5%
- 内存使用增加不超过10%
- 文件系统占用不超过1KB

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**：K210开发者和设备操作员
- **使用场景**：自动化设备、机器人控制、视觉追踪应用

### 4.2 用户故事
1. **作为操作员**，我希望系统能在第一次检测到目标时自动记录位置，以便后续复位使用
2. **作为开发者**，我希望位置数据能在断电后保持，确保设备重启后能恢复到记录位置
3. **作为用户**，我希望能通过简单命令手动触发复位操作
4. **作为维护人员**，我希望能查询当前的锁定状态和保存的位置信息

## 5. 功能规格详述

### 5.1 数据结构设计

#### 5.1.1 位置数据结构
```json
{
    "x": 2048,
    "y": 2048,
    "timestamp": 1704672000,
    "locked": true,
    "version": "1.0"
}
```

#### 5.1.2 数据字段说明
| 字段 | 类型 | 说明 | 是否必需 |
|------|------|------|----------|
| x | int | X轴舵机位置 (1000-3000) | 是 |
| y | int | Y轴舵机位置 (1000-3000) | 是 |
| timestamp | int | 记录时间戳 | 是 |
| locked | bool | 锁定状态标志 | 是 |
| version | string | 数据格式版本 | 是 |

### 5.2 文件存储规范

#### 5.2.1 存储路径
- **主文件**：`/flash/servo_position.json`
- **备份文件**：`/flash/servo_position_backup.json`

#### 5.2.2 文件格式
- 使用JSON格式存储
- 采用ujson模块进行序列化/反序列化
- 文件大小限制：< 1KB

### 5.3 模块接口设计

#### 5.3.1 核心接口函数

```python
def save_position_to_file(position_data):
    """保存位置数据到文件
    Args:
        position_data (dict): 位置数据字典
    Returns:
        bool: 保存成功返回True，失败返回False
    """

def load_position_from_file():
    """从文件加载位置数据
    Returns:
        dict or None: 成功返回位置数据，失败返回None
    """

def reset_to_saved_position():
    """复位舵机到保存的位置
    Returns:
        bool: 复位成功返回True，失败返回False
    """

def validate_position_data(data):
    """验证位置数据格式
    Args:
        data (dict): 待验证的数据
    Returns:
        bool: 数据有效返回True，无效返回False
    """
```

### 5.4 异常处理策略

#### 5.4.1 文件操作异常
- **文件不存在**：返回默认值，不报错
- **读写权限错误**：记录日志，使用备份文件
- **磁盘空间不足**：清理旧文件，重试操作
- **文件损坏**：使用备份文件恢复

#### 5.4.2 JSON解析异常
- **格式错误**：使用默认数据结构
- **字段缺失**：补充默认值
- **类型错误**：进行类型转换

#### 5.4.3 舵机控制异常
- **通信失败**：重试3次
- **位置超限**：限制在有效范围内
- **响应超时**：记录错误，继续执行

### 5.5 系统集成方案

#### 5.5.1 集成点分析
1. **初始化阶段**：在`hardware_init()`后加载保存的位置
2. **检测循环**：在矩形检测循环中添加锁定逻辑
3. **显示更新**：在LCD显示中添加状态信息
4. **命令处理**：在主循环中添加串口命令检测

#### 5.5.2 代码集成位置
- **第48行后**：添加位置管理模块导入
- **第84行后**：添加位置数据加载
- **第102行内**：添加首次检测锁定逻辑
- **第143行后**：添加状态显示
- **第90行内**：添加串口命令检测

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 位置数据的JSON格式存储
- 首次检测自动锁定机制
- 舵机复位功能
- 基本的用户交互接口
- 异常处理和错误恢复
- 状态显示和反馈

### 6.2 排除功能 (Out of Scope)
- 多个位置点的记录
- 复杂的用户界面
- 网络远程控制
- 高级的数据分析功能
- 实时数据同步

## 7. 依赖与风险

### 7.1 技术依赖
- K210 MicroPython环境
- ujson模块支持
- 文件系统读写权限
- 现有舵机控制协议

### 7.2 潜在风险
| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 文件系统故障 | 高 | 低 | 实现备份机制 |
| JSON解析失败 | 中 | 中 | 添加数据验证 |
| 舵机通信异常 | 高 | 低 | 多次重试机制 |
| 内存不足 | 中 | 低 | 优化数据结构 |

## 8. 发布初步计划

### 8.1 开发阶段
1. **架构设计** (1天)：完成详细设计文档
2. **核心开发** (3天)：实现存储和复位功能
3. **集成测试** (2天)：系统集成和功能测试
4. **优化调试** (1天)：性能优化和bug修复

### 8.2 测试计划
- **单元测试**：各模块功能验证
- **集成测试**：系统整体功能测试
- **压力测试**：长时间运行稳定性测试
- **断电测试**：数据持久化验证

### 8.3 验收标准
- 所有功能按需求正常工作
- 通过断电恢复测试
- 性能指标达到要求
- 代码质量符合规范

---

**文档状态**：已完成
**下一步**：提交技术架构设计评审
