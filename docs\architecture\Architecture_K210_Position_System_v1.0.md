# K210位置记录系统架构设计文档

## 1. 架构概述

### 1.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    K210矩形追踪系统                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   图像处理模块   │  │   位置管理模块   │  │   舵机控制模块   │ │
│  │                │  │   (新增)        │  │                │ │
│  │ - 矩形检测      │  │ - 位置记录      │  │ - 舵机驱动      │ │
│  │ - 中心计算      │  │ - 数据存储      │  │ - 位置控制      │ │
│  │ - PID控制       │  │ - 状态管理      │  │ - 复位功能      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           └─────────────────────┼─────────────────────┘        │
│                                 │                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   显示模块      │  │   存储模块      │  │   交互模块      │ │
│  │                │  │                │  │                │ │
│  │ - LCD显示       │  │ - JSON序列化    │  │ - 串口命令      │ │
│  │ - 状态指示      │  │ - 文件I/O       │  │ - 状态查询      │ │
│  │ - 信息反馈      │  │ - 异常处理      │  │ - 手动控制      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 数据流图

```
输入图像 → 矩形检测 → 首次检测判断 → 位置记录 → 数据存储
    │                      │              │          │
    │                      ↓              ↓          ↓
    └→ PID控制 → 舵机驱动 ← 锁定状态 ← 位置管理 ← 文件系统
                    │
                    ↓
              LCD状态显示 ← 串口命令 ← 用户交互
```

## 2. 模块详细设计

### 2.1 位置管理模块 (Position Manager)

#### 2.1.1 模块职责
- 管理位置记录的生命周期
- 控制锁定/解锁状态
- 协调存储和复位操作
- 提供状态查询接口

#### 2.1.2 核心数据结构

```python
class PositionManager:
    def __init__(self):
        self.is_locked = False
        self.first_detection = True
        self.saved_position = None
        self.current_position = {"x": 2048, "y": 2048}
        
    # 状态管理
    def lock_position(self, x, y)
    def unlock_position(self)
    def is_position_locked(self)
    
    # 位置操作
    def save_current_position(self, x, y)
    def get_saved_position(self)
    def reset_to_saved_position(self)
```

#### 2.1.3 状态机设计

```
初始状态 → 检测中 → 已锁定 → 复位中
    ↑         │        │        │
    └─────────┴────────┴────────┘
    
状态转换条件：
- 初始状态 → 检测中：启动系统
- 检测中 → 已锁定：首次检测到矩形
- 已锁定 → 复位中：执行复位命令
- 复位中 → 已锁定：复位完成
- 任意状态 → 初始状态：解锁命令
```

### 2.2 存储模块 (Storage Module)

#### 2.2.1 文件系统架构

```
/flash/
├── servo_position.json      # 主数据文件
├── servo_position_backup.json  # 备份文件
└── position_log.txt         # 操作日志(可选)
```

#### 2.2.2 数据序列化格式

```python
# 标准位置数据格式
position_data = {
    "version": "1.0",
    "timestamp": 1704672000,
    "position": {
        "x": 2048,
        "y": 2048
    },
    "status": {
        "locked": True,
        "detection_count": 1
    },
    "metadata": {
        "device_id": "K210_001",
        "firmware_version": "1.0"
    }
}
```

#### 2.2.3 异常处理策略

```python
def robust_file_operation(operation, *args, **kwargs):
    """健壮的文件操作包装器"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            return operation(*args, **kwargs)
        except OSError as e:
            if attempt == max_retries - 1:
                # 最后一次尝试失败，使用备份策略
                return handle_file_error(e)
            time.sleep(0.1)  # 短暂延迟后重试
```

### 2.3 舵机控制增强模块

#### 2.3.1 复位控制策略

```python
def enhanced_servo_reset(target_x, target_y):
    """增强的舵机复位功能"""
    # 多步骤复位策略
    steps = [
        {"x": target_x, "y": target_y, "interval": 1000},  # 慢速移动
        {"x": target_x, "y": target_y, "interval": 500},   # 中速确认
        {"x": target_x, "y": target_y, "interval": 100}    # 快速微调
    ]
    
    for step in steps:
        for retry in range(3):  # 每步重试3次
            func_servo(ID1, step["x"], step["interval"])
            func_servo(ID2, step["y"], step["interval"])
            time.sleep(step["interval"] / 1000.0)
```

## 3. 接口规范

### 3.1 内部接口

#### 3.1.1 位置管理接口

```python
# 位置记录接口
def record_position(x, y, timestamp=None):
    """记录当前位置"""
    
def get_recorded_position():
    """获取记录的位置"""
    
def clear_recorded_position():
    """清除记录的位置"""

# 状态管理接口  
def set_lock_state(locked):
    """设置锁定状态"""
    
def get_lock_state():
    """获取锁定状态"""
    
def get_system_status():
    """获取系统完整状态"""
```

#### 3.1.2 存储接口

```python
# 文件操作接口
def save_data_to_file(data, filepath):
    """保存数据到文件"""
    
def load_data_from_file(filepath):
    """从文件加载数据"""
    
def backup_data_file(filepath):
    """备份数据文件"""
    
def restore_from_backup(filepath):
    """从备份恢复数据"""
```

### 3.2 外部接口

#### 3.2.1 串口命令接口

```
命令格式：<COMMAND>[:<PARAMS>]\r\n

支持的命令：
- LOCK          # 锁定当前位置
- UNLOCK        # 解锁恢复追踪
- RESET         # 复位到保存位置  
- STATUS        # 查询系统状态
- SAVE:<x>,<y>  # 手动保存指定位置
- GET_POS       # 获取当前位置
```

#### 3.2.2 状态反馈接口

```
状态信息格式：
{
    "status": "LOCKED|TRACKING|RESETTING",
    "position": {"x": 2048, "y": 2048},
    "saved_position": {"x": 2048, "y": 2048},
    "timestamp": 1704672000
}
```

## 4. 集成方案

### 4.1 代码集成点

#### 4.1.1 主函数修改点

```python
def main():
    # 1. 初始化阶段 (第48行后)
    position_manager = PositionManager()
    position_manager.load_saved_position()
    
    # 2. 主循环修改 (第87行)
    while True:
        # 原有代码...
        
        # 3. 矩形检测后 (第102行)
        if rectangles:
            if position_manager.should_lock():
                position_manager.lock_current_position(servo_x_pos, servo_y_pos)
            
            if not position_manager.is_locked:
                # 原有PID控制逻辑
                pass
        
        # 4. 命令处理 (第90行)
        command = check_serial_command()
        if command:
            handle_command(command, position_manager)
        
        # 5. 状态显示 (第143行)
        display_system_status(position_manager)
```

### 4.2 内存管理策略

#### 4.2.1 内存使用优化

```python
# 使用对象池减少内存分配
class ObjectPool:
    def __init__(self):
        self._position_objects = []
        
    def get_position_object(self):
        if self._position_objects:
            return self._position_objects.pop()
        return {"x": 0, "y": 0, "timestamp": 0}
    
    def return_position_object(self, obj):
        obj.clear()
        self._position_objects.append(obj)
```

#### 4.2.2 垃圾回收策略

```python
# 定期清理策略
def periodic_cleanup():
    if frame_count % 100 == 0:  # 每100帧清理一次
        gc.collect()
        cleanup_temp_files()
```

## 5. 性能考虑

### 5.1 实时性保证

- 文件I/O操作异步化，不阻塞主循环
- 串口命令检测使用非阻塞方式
- 状态显示更新频率限制在10Hz以内

### 5.2 资源使用限制

- 内存增加 < 2KB
- 文件系统占用 < 1KB  
- CPU使用率增加 < 5%

## 6. 安全性设计

### 6.1 数据完整性

- 使用CRC校验确保数据完整性
- 双文件备份机制
- 原子性写入操作

### 6.2 异常恢复

- 自动故障检测和恢复
- 优雅降级机制
- 错误日志记录

---

**架构设计状态**：已完成
**版本**：v1.0
**审核状态**：待技术评审
